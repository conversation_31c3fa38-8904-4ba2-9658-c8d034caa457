<?php
/**
 * Debug Upload Test - Check upload functionality
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h1>Upload Debug Information</h1>";

echo "<h2>Upload Configuration</h2>";
echo "UPLOAD_PATH: " . UPLOAD_PATH . "<br>";
echo "UPLOAD_URL: " . UPLOAD_URL . "<br>";
echo "MAX_FILE_SIZE: " . (MAX_FILE_SIZE / 1024 / 1024) . " MB<br>";

echo "<h2>Directory Status</h2>";
if (file_exists(UPLOAD_PATH)) {
    echo "✅ Upload directory exists<br>";
    if (is_writable(UPLOAD_PATH)) {
        echo "✅ Upload directory is writable<br>";
    } else {
        echo "❌ Upload directory is NOT writable<br>";
    }
} else {
    echo "❌ Upload directory does not exist<br>";
}

echo "<h2>PHP Upload Settings</h2>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "file_uploads: " . (ini_get('file_uploads') ? 'On' : 'Off') . "<br>";

if ($_POST && isset($_FILES['test_file'])) {
    echo "<h2>Upload Test Result</h2>";
    $result = uploadFile($_FILES['test_file'], ['jpg', 'jpeg', 'png', 'webp']);
    if ($result['success']) {
        echo "✅ Upload successful!<br>";
        echo "File URL: " . $result['url'] . "<br>";
        echo "Filename: " . $result['filename'] . "<br>";
    } else {
        echo "❌ Upload failed: " . $result['message'] . "<br>";
    }
    
    echo "<h3>File Details</h3>";
    echo "Original name: " . $_FILES['test_file']['name'] . "<br>";
    echo "Size: " . $_FILES['test_file']['size'] . " bytes<br>";
    echo "Type: " . $_FILES['test_file']['type'] . "<br>";
    echo "Error code: " . $_FILES['test_file']['error'] . "<br>";
}
?>

<form method="POST" enctype="multipart/form-data">
    <h2>Test Upload</h2>
    <input type="file" name="test_file" accept="image/*" required>
    <button type="submit">Test Upload</button>
</form>
