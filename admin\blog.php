<?php
/**
 * Blog/News Management - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Start session for admin authentication
session_start();

// Check admin authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

$db = Database::getConnection();
$message = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_post':
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $excerpt = sanitizeInput($_POST['excerpt']);
                $content = $_POST['content']; // Keep HTML formatting
                $category = sanitizeInput($_POST['category']);
                $tags = sanitizeInput($_POST['tags']);
                $status = sanitizeInput($_POST['status']);
                $author = sanitizeInput($_POST['author'] ?: 'Monolith Design Team');
                $featured_image = '';
                
                // Handle featured image upload with debug output
                if (isset($_FILES['featured_image'])) {
                    if ($_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload['success']) {
                            $featured_image = $upload['url'];
                        } else {
                            $error = 'Error uploading featured image: ' . $upload['message'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                        }
                    } else if ($_FILES['featured_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                        $error = 'Featured image upload error: ' . $_FILES['featured_image']['error'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                    }
                }
                
                if (empty($error)) {
                    $stmt = $db->prepare("INSERT INTO blog_posts (title, slug, excerpt, content, category, tags, status, author, featured_image, published_at, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
                    $published_at = ($status === 'published') ? date('Y-m-d H:i:s') : null;
                    
                    if ($stmt->execute([$title, $slug, $excerpt, $content, $category, $tags, $status, $author, $featured_image, $published_at])) {
                        $message = 'Blog post added successfully!';
                    } else {
                        $error = 'Error adding blog post to database.';
                    }
                }
                break;
                
            case 'update_post':
                $id = (int)$_POST['id'];
                $title = sanitizeInput($_POST['title']);
                $slug = createSlug($_POST['slug'] ?: $title);
                $excerpt = sanitizeInput($_POST['excerpt']);
                $content = $_POST['content'];
                $category = sanitizeInput($_POST['category']);
                $tags = sanitizeInput($_POST['tags']);
                $status = sanitizeInput($_POST['status']);
                $author = sanitizeInput($_POST['author']);
                
                // Initialize parameters for update query
                $featured_image_sql = '';
                $params = [$title, $slug, $excerpt, $content, $category, $tags, $status, $author];
                // Handle featured image upload with debug output
                if (isset($_FILES['featured_image'])) {
                    if ($_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                        $upload = uploadFile($_FILES['featured_image'], ['jpg', 'jpeg', 'png', 'webp']);
                        if ($upload['success']) {
                            $featured_image_sql = ', featured_image = ?';
                            $params[] = $upload['url'];
                        } else {
                            $error = 'Error uploading featured image: ' . $upload['message'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                            break;
                        }
                    } else if ($_FILES['featured_image']['error'] !== UPLOAD_ERR_NO_FILE) {
                        $error = 'Featured image upload error: ' . $_FILES['featured_image']['error'] . '<br>Debug: ' . print_r($_FILES['featured_image'], true);
                        break;
                    }
                }
                
                $published_at_sql = '';
                if ($status === 'published') {
                    // Get current post to check if it was draft
                    $stmt = $db->prepare("SELECT status, published_at FROM blog_posts WHERE id = ?");
                    $stmt->execute([$id]);
                    $current_post = $stmt->fetch();
                    
                    if ($current_post['status'] !== 'published' || !$current_post['published_at']) {
                        $published_at_sql = ', published_at = NOW()';
                    }
                }
                
                $params[] = $id;
                $stmt = $db->prepare("UPDATE blog_posts SET title = ?, slug = ?, excerpt = ?, content = ?, category = ?, tags = ?, status = ?, author = ?, updated_at = NOW(){$featured_image_sql}{$published_at_sql} WHERE id = ?");
                
                if ($stmt->execute($params)) {
                    $message = 'Blog post updated successfully!';
                } else {
                    $error = 'Error updating blog post.';
                }
                break;
                
            case 'delete_post':
                $id = (int)$_POST['id'];
                $stmt = $db->prepare("DELETE FROM blog_posts WHERE id = ?");
                if ($stmt->execute([$id])) {
                    $message = 'Blog post deleted successfully!';
                } else {
                    $error = 'Error deleting blog post.';
                }
                break;
        }
    }
}

// Get posts for listing
$posts = [];
$stmt = $db->prepare("SELECT * FROM blog_posts ORDER BY created_at DESC");
$stmt->execute();
$posts = $stmt->fetchAll();

// Get post for editing if requested
$edit_post = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $stmt = $db->prepare("SELECT * FROM blog_posts WHERE id = ?");
    $stmt->execute([$_GET['edit']]);
    $edit_post = $stmt->fetch();
}

// Note: createSlug() function is now in includes/functions.php to avoid duplication
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Management - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #1A1A1A;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav {
            background: #2c3e50;
            padding: 1rem 2rem;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            margin-right: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: #E67E22;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .admin-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-card-header {
            background: #34495e;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-card-body {
            padding: 1.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }
        
        input[type="text"],
        input[type="email"],
        textarea,
        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus,
        input[type="email"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #E67E22;
        }
        
        textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .content-editor {
            min-height: 300px;
        }
        
        .btn {
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #d35400;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-small {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .message {
            background: #fdf6f0;
            color: #E67E22;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .error {
            background: #fadbd8;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .posts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .posts-table th,
        .posts-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .posts-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .posts-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-published {
            background: #d4edda;
            color: #155724;
        }
        
        .status-draft {
            background: #fff3cd;
            color: #856404;
        }
        
        .image-preview {
            max-width: 150px;
            max-height: 100px;
            border-radius: 4px;
            margin-top: 0.5rem;
        }
        
        .post-form {
            display: none;
        }
        
        .post-form.active {
            display: block;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .posts-table {
                font-size: 0.9rem;
            }
            
            .posts-table th,
            .posts-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Blog/News Management</h1>
        <div>
            <a href="<?php echo siteUrl('news'); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Blog</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php" class="active">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <!-- Add/Edit Post Form -->
        <div class="admin-card">
            <div class="admin-card-header">
                <span><?php echo $edit_post ? 'Edit Blog Post' : 'Add New Blog Post'; ?></span>
                <button type="button" class="btn btn-secondary btn-small" onclick="toggleForm()">
                    <?php echo $edit_post ? 'Cancel Edit' : 'Add New Post'; ?>
                </button>
            </div>
            
            <div class="admin-card-body">
                <form method="POST" enctype="multipart/form-data" class="post-form <?php echo $edit_post ? 'active' : ''; ?>" id="postForm">
                    <input type="hidden" name="MAX_FILE_SIZE" value="<?php echo MAX_FILE_SIZE; ?>">
                    <input type="hidden" name="action" value="<?php echo $edit_post ? 'update_post' : 'add_post'; ?>">
                    <?php if ($edit_post): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_post['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="title">Post Title *</label>
                            <input type="text" id="title" name="title" required 
                                   value="<?php echo $edit_post ? htmlspecialchars($edit_post['title']) : ''; ?>"
                                   onkeyup="generateSlug()">
                        </div>
                        
                        <div class="form-group">
                            <label for="slug">URL Slug</label>
                            <input type="text" id="slug" name="slug" 
                                   value="<?php echo $edit_post ? htmlspecialchars($edit_post['slug']) : ''; ?>"
                                   placeholder="Auto-generated from title">
                            <small style="color: #666; display: block; margin-top: 0.25rem;">Leave empty to auto-generate from title</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Architecture" <?php echo ($edit_post && $edit_post['category'] === 'Architecture') ? 'selected' : ''; ?>>Architecture</option>
                                <option value="Engineering" <?php echo ($edit_post && $edit_post['category'] === 'Engineering') ? 'selected' : ''; ?>>Engineering</option>
                                <option value="Design" <?php echo ($edit_post && $edit_post['category'] === 'Design') ? 'selected' : ''; ?>>Design</option>
                                <option value="Construction" <?php echo ($edit_post && $edit_post['category'] === 'Construction') ? 'selected' : ''; ?>>Construction</option>
                                <option value="Sustainability" <?php echo ($edit_post && $edit_post['category'] === 'Sustainability') ? 'selected' : ''; ?>>Sustainability</option>
                                <option value="Innovation" <?php echo ($edit_post && $edit_post['category'] === 'Innovation') ? 'selected' : ''; ?>>Innovation</option>
                                <option value="News" <?php echo ($edit_post && $edit_post['category'] === 'News') ? 'selected' : ''; ?>>Company News</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" required>
                                <option value="draft" <?php echo ($edit_post && $edit_post['status'] === 'draft') ? 'selected' : ''; ?>>Draft</option>
                                <option value="published" <?php echo ($edit_post && $edit_post['status'] === 'published') ? 'selected' : ''; ?>>Published</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="author">Author</label>
                            <input type="text" id="author" name="author" 
                                   value="<?php echo $edit_post ? htmlspecialchars($edit_post['author']) : 'Monolith Design Team'; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="tags">Tags</label>
                            <input type="text" id="tags" name="tags" 
                                   value="<?php echo $edit_post ? htmlspecialchars($edit_post['tags']) : ''; ?>"
                                   placeholder="Separate tags with commas">
                            <small style="color: #666; display: block; margin-top: 0.25rem;">E.g., modern, sustainable, commercial</small>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="excerpt">Excerpt/Summary</label>
                            <textarea id="excerpt" name="excerpt" rows="3" 
                                      placeholder="Brief summary of the post for listings and meta description"><?php echo $edit_post ? htmlspecialchars($edit_post['excerpt']) : ''; ?></textarea>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="featured_image">Featured Image</label>
                            <?php if ($edit_post && $edit_post['featured_image']): ?>
                                <div style="margin-bottom: 1rem;">
                                    <img src="<?php echo $edit_post['featured_image']; ?>" alt="Current Featured Image" class="image-preview">
                                    <p style="margin: 0.5rem 0; color: #666; font-size: 0.9rem;">Current featured image</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" id="featured_image" name="featured_image" accept="image/*">
                            <small style="color: #666; display: block; margin-top: 0.25rem;">Recommended size: 1200x600px or larger. JPG, PNG, WebP supported.</small>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="content">Content *</label>
                            <textarea id="content" name="content" class="content-editor" required 
                                      placeholder="Full blog post content. You can use HTML formatting."><?php echo $edit_post ? htmlspecialchars($edit_post['content']) : ''; ?></textarea>
                            <small style="color: #666; display: block; margin-top: 0.25rem;">
                                HTML formatting supported. Use &lt;h2&gt;, &lt;h3&gt;, &lt;p&gt;, &lt;ul&gt;, &lt;ol&gt;, &lt;blockquote&gt;, etc.
                            </small>
                        </div>
                    </div>
                    
                    <div style="margin-top: 2rem;">
                        <button type="submit" class="btn"><?php echo $edit_post ? 'Update Post' : 'Add Post'; ?></button>
                        <button type="button" class="btn btn-secondary" onclick="cancelForm()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Posts List -->
        <div class="admin-card">
            <div class="admin-card-header">
                <span>All Blog Posts (<?php echo count($posts); ?>)</span>
            </div>
            
            <div class="admin-card-body">
                <?php if (empty($posts)): ?>
                    <p style="text-align: center; color: #666; padding: 2rem;">No blog posts found. <a href="#" onclick="toggleForm()">Add your first post</a>.</p>
                <?php else: ?>
                    <table class="posts-table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Author</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($posts as $post): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($post['title']); ?></strong>
                                    <?php if ($post['status'] === 'published'): ?>
                                        <br><small><a href="<?php echo siteUrl('news/' . $post['slug']); ?>" target="_blank" style="color: #E67E22;">View Post</a></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($post['category']); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $post['status']; ?>">
                                        <?php echo ucfirst($post['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($post['author']); ?></td>
                                <td>
                                    <?php if ($post['published_at']): ?>
                                        <?php echo date('M j, Y', strtotime($post['published_at'])); ?>
                                    <?php else: ?>
                                        <em>Not published</em>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?edit=<?php echo $post['id']; ?>" class="btn btn-secondary btn-small">Edit</a>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this post?');">
                                            <input type="hidden" name="action" value="delete_post">
                                            <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
                                            <button type="submit" class="btn btn-danger btn-small">Delete</button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script>
        function toggleForm() {
            const form = document.getElementById('postForm');
            form.classList.toggle('active');
            
            if (form.classList.contains('active')) {
                document.getElementById('title').focus();
            }
        }
        
        function cancelForm() {
            window.location.href = 'blog.php';
        }
        
        function generateSlug() {
            const title = document.getElementById('title').value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        }
        
        // Auto-resize content textarea
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('content');
            if (textarea) {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            }
        });
    </script>
</body>
</html>
