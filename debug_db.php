<?php
/**
 * Database Debug - Check database structure and content
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

try {
    $db = Database::getConnection();
    
    echo "<h1>Database Debug Information</h1>";
    
    // Check blog_posts table structure
    echo "<h2>Blog Posts Table Structure</h2>";
    $stmt = $db->query("DESCRIBE blog_posts");
    $columns = $stmt->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        foreach ($col as $value) {
            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Check projects table structure
    echo "<h2>Projects Table Structure</h2>";
    $stmt = $db->query("DESCRIBE projects");
    $columns = $stmt->fetchAll();
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        foreach ($col as $value) {
            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Check recent blog posts
    echo "<h2>Recent Blog Posts</h2>";
    $stmt = $db->query("SELECT id, title, featured_image, created_at FROM blog_posts ORDER BY created_at DESC LIMIT 5");
    $posts = $stmt->fetchAll();
    if ($posts) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Title</th><th>Featured Image</th><th>Created</th></tr>";
        foreach ($posts as $post) {
            echo "<tr>";
            echo "<td>" . $post['id'] . "</td>";
            echo "<td>" . htmlspecialchars($post['title']) . "</td>";
            echo "<td>" . htmlspecialchars($post['featured_image']) . "</td>";
            echo "<td>" . $post['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No blog posts found.";
    }
    
    // Check recent projects
    echo "<h2>Recent Projects</h2>";
    $stmt = $db->query("SELECT id, title, featured_image, created_at FROM projects ORDER BY created_at DESC LIMIT 5");
    $projects = $stmt->fetchAll();
    if ($projects) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Title</th><th>Featured Image</th><th>Created</th></tr>";
        foreach ($projects as $project) {
            echo "<tr>";
            echo "<td>" . $project['id'] . "</td>";
            echo "<td>" . htmlspecialchars($project['title']) . "</td>";
            echo "<td>" . htmlspecialchars($project['featured_image']) . "</td>";
            echo "<td>" . $project['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No projects found.";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}
?>
